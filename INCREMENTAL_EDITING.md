# Incremental Code Editing Feature

## Overview

The incremental code editing feature allows the AI to make targeted changes to existing code instead of regenerating the entire project. This provides a much better development experience by:

- ✅ **Preserving existing functionality** - Only modifies what's necessary
- ✅ **Adding new features incrementally** - Builds upon existing code
- ✅ **Maintaining code structure** - Respects existing patterns and organization
- ✅ **Faster generation** - Only processes changed files
- ✅ **Better user experience** - Clear feedback on what changed

## How It Works

### 1. Intelligent Detection

The system automatically determines whether to use incremental editing or full regeneration based on:

**Incremental Edit Triggers:**
- Keywords: `add`, `create`, `implement`, `modify`, `update`, `fix`, `enhance`
- Existing files in the project
- Feature-specific requests

**Full Regeneration Triggers:**
- Keywords: `rebuild`, `recreate`, `start over`, `from scratch`, `rewrite everything`
- No existing files
- Complete project changes

### 2. Edit Types

The system categorizes edits into different types:

- **`feature`** - Adding new functionality (default)
- **`bugfix`** - Fixing errors or issues
- **`style`** - Design and CSS changes
- **`refactor`** - Code organization improvements
- **`component`** - Creating new UI components

### 3. Response Structure

Incremental edits return a structured response:

```json
{
  "editType": "incremental",
  "summary": "Added search functionality to the todo app",
  "newFiles": {
    "/components/SearchBar.js": {
      "code": "// Complete component code",
      "reason": "New search component for filtering todos"
    }
  },
  "modifiedFiles": {
    "/App.js": {
      "code": "// Updated App.js with search integration",
      "changes": "Added SearchBar component and search state",
      "reason": "Integrate search functionality"
    }
  },
  "unchangedFiles": ["/components/TodoItem.js", "/styles.css"],
  "usageInstructions": "Use the search bar to filter todos by text",
  "usedShadcnComponents": ["Input", "Button"]
}
```

## API Endpoints

### `/api/edit-code` (POST)

Handles incremental code editing requests.

**Request:**
```json
{
  "prompt": "Add a search feature to filter todos",
  "existingFiles": {
    "/App.js": { "code": "..." },
    "/components/TodoItem.js": { "code": "..." }
  },
  "editType": "feature"
}
```

**Response:**
```json
{
  "editType": "incremental",
  "summary": "Added search functionality",
  "newFiles": { ... },
  "modifiedFiles": { ... },
  "unchangedFiles": [...],
  "usageInstructions": "...",
  "usedShadcnComponents": [...]
}
```

## Example Usage Scenarios

### 1. Adding a New Feature

**User Prompt:** "Add a delete button to each todo item"

**System Response:**
- **Edit Type:** `feature`
- **New Files:** None (uses existing components)
- **Modified Files:** 
  - `/components/TodoItem.js` - Adds delete button and handler
  - `/App.js` - Adds delete function to state management
- **Unchanged Files:** All other files remain untouched

### 2. Creating a New Component

**User Prompt:** "Create a modal for editing todo items"

**System Response:**
- **Edit Type:** `component`
- **New Files:**
  - `/components/EditModal.js` - Complete modal component
  - `/components/ui/dialog.tsx` - shadcn Dialog component (if needed)
- **Modified Files:**
  - `/components/TodoItem.js` - Adds edit button
  - `/App.js` - Integrates modal state
- **Unchanged Files:** Other components remain the same

### 3. Fixing a Bug

**User Prompt:** "Fix the issue where completed todos don't save properly"

**System Response:**
- **Edit Type:** `bugfix`
- **New Files:** None
- **Modified Files:**
  - `/App.js` - Fixes the save logic for completed todos
- **Unchanged Files:** All UI components remain untouched

## Benefits Over Full Regeneration

| Aspect | Incremental Editing | Full Regeneration |
|--------|-------------------|------------------|
| **Speed** | ⚡ Fast - only processes changes | 🐌 Slow - regenerates everything |
| **Preservation** | ✅ Keeps existing code intact | ❌ May lose custom modifications |
| **Clarity** | 📋 Clear change summary | 🤔 Hard to see what changed |
| **Risk** | 🛡️ Low risk of breaking existing features | ⚠️ Higher risk of regressions |
| **User Experience** | 😊 Smooth, predictable updates | 😰 Potentially disruptive |

## Implementation Details

### File Structure

```
src/
├── app/api/edit-code/
│   └── route.ts              # Incremental editing API endpoint
├── lib/
│   └── incremental-editor.ts # Core incremental editing logic
├── components/customs/workspace/
│   └── CodeView.tsx          # Updated to support both modes
└── data/
    └── Lookup.tsx            # Added incremental suggestions
```

### Key Components

1. **`IncrementalEditor`** - Core service class
2. **`/api/edit-code`** - API endpoint for incremental edits
3. **Enhanced CodeView** - Supports both editing modes
4. **Smart Detection** - Automatically chooses the right approach

## User Interface

### Loading States

- **Full Generation:** "Generating code..."
- **Incremental Edit:** "Adding feature..." with subtitle "Making targeted changes to existing code"

### Change Summary

After incremental edits, users see a detailed summary:
- 📁 New files created
- ✏️ Files modified  
- ✅ Files preserved
- 🎨 shadcn/ui components used
- 💡 Usage instructions

## Future Enhancements

- **Visual diff viewer** - Show exactly what changed
- **Undo/redo functionality** - Revert incremental changes
- **Change history** - Track all modifications over time
- **Conflict resolution** - Handle overlapping changes
- **Preview mode** - See changes before applying them
