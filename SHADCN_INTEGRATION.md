# Shadcn/UI Component Integration System

## Overview

This system automatically integrates shadcn/ui components into AI-generated code, ensuring that modern, accessible UI components are used instead of basic HTML elements.

## How It Works

### 1. AI Prompt Enhancement
The `CODE_GEN_PROMPT` in `src/data/prompt.tsx` has been enhanced to:
- Instruct the AI to prioritize shadcn/ui components
- Provide examples of proper component usage
- Include import statements for all available components
- Specify component variants and usage patterns

### 2. Component Detection System
The `ShadcnComponentDetector` in `src/lib/shadcn-detector.ts`:
- Analyzes generated code for UI patterns (buttons, inputs, cards, etc.)
- Maps detected patterns to appropriate shadcn components
- Generates required import statements
- Identifies necessary dependencies

### 3. Automatic Integration
The enhanced API route in `src/app/api/gen-ai-code/route.ts`:
- Processes AI-generated code through the detection system
- Adds required component files and utilities
- Updates dependencies automatically
- Enhances the response with shadcn metadata

### 4. Available Components

The system supports all shadcn/ui components including:
- **Form Components**: Button, Input, Label, Textarea, Select, Checkbox, RadioGroup, Switch
- **Layout Components**: Card, Tabs, Sheet, Dialog, AlertDialog
- **Feedback Components**: Alert, Badge, Progress, Skeleton, Tooltip
- **Data Components**: Table, Avatar, Calendar, Command
- **Navigation Components**: Breadcrumb, Pagination

## Usage Example

### Before (Basic HTML):
```jsx
<button className="bg-blue-500 text-white px-4 py-2 rounded">
  Click me
</button>
<input type="text" placeholder="Enter text..." className="border p-2" />
```

### After (Shadcn Components):
```jsx
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

<Button variant="default">Click me</Button>
<Input type="text" placeholder="Enter text..." />
```

## Configuration

### Dependencies
All required shadcn dependencies are automatically included in `src/data/Lookup.tsx`:
- Radix UI primitives
- Class variance authority
- Tailwind utilities
- React Hook Form integration

### Component Mapping
The system maps HTML patterns to shadcn components:
- `<button>` → `<Button>`
- `<input>` → `<Input>`
- `<div className="card">` → `<Card>`
- `<input type="checkbox">` → `<Checkbox>`
- Modal patterns → `<Dialog>` or `<AlertDialog>`

## Testing

Run the todo app example to see the system in action:
1. Create a new workspace
2. Enter prompt: "Create a todo app with add, delete, and toggle functionality"
3. The generated code will automatically use shadcn components

## Benefits

1. **Consistent Design**: All components follow the same design system
2. **Accessibility**: Built-in ARIA attributes and keyboard navigation
3. **Type Safety**: Full TypeScript support with proper prop types
4. **Customization**: Easy theming with CSS variables
5. **Performance**: Optimized components with proper React patterns

## File Structure

```
src/
├── components/ui/          # All shadcn components
├── lib/
│   ├── utils.ts           # cn() utility function
│   ├── shadcn-detector.ts # Component detection system
│   └── test-shadcn-detection.ts # Test cases
├── data/
│   ├── Lookup.tsx         # Dependencies and component mapping
│   └── prompt.tsx         # Enhanced AI prompts
└── app/api/gen-ai-code/
    └── route.ts           # Enhanced API with auto-integration
```

## Future Enhancements

1. **Smart Component Suggestions**: Analyze user intent to suggest optimal components
2. **Theme Integration**: Automatic dark/light mode support
3. **Component Variants**: Intelligent selection of component variants based on context
4. **Performance Optimization**: Lazy loading of unused components
5. **Custom Component Support**: Integration with custom design system components

## Troubleshooting

### Common Issues:
1. **Missing Imports**: Ensure all required dependencies are in package.json
2. **Style Conflicts**: Check Tailwind CSS configuration
3. **Type Errors**: Verify TypeScript configuration and component props

### Debug Mode:
The system logs detected components and imports to the console for debugging.
