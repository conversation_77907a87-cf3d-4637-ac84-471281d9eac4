'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger, 
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { useIsMobile } from '@/hooks/use-mobile'
import { 
  Zap, 
  Sparkles, 
  LogOut, 
  CreditCard, 
  User, 
  Menu,
  X
} from 'lucide-react'
import HateableLogo from '../customs/logo'

interface NavbarProps {
  variant?: 'landing' | 'app'
  setIsLoginDialogOpen?: (open: boolean) => void
}

const Navbar: React.FC<NavbarProps> = ({ 
  variant = 'app', 
  setIsLoginDialogOpen 
}) => {
  const { data: session } = useSession()
  const router = useRouter()
  const pathname = usePathname()
  const isMobile = useIsMobile()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const isLandingPage = variant === 'landing'
  const isWorkspacePage = pathname?.startsWith('/workspace')

  const navigationLinks = [
    { name: 'Features', href: '/#features' },
    { name: 'Pricing', href: '/#pricing' },
    { name: 'Docs', href: '/docs' },
  ]

  const handleAuthAction = () => {
    if (setIsLoginDialogOpen) {
      setIsLoginDialogOpen(true)
    } else {
      router.push('/Thinkpad')
    }
  }

  const Logo = () => (
    <Link href="/" className="flex items-center gap-3 group">
     <HateableLogo/>
      <div className="flex items-center gap-2">
        <span className="text-xl font-bold hateable-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent group-hover:from-purple-300 group-hover:to-pink-300 transition-all">
          Hateable
        </span>
        <Badge variant="secondary" className="text-xs px-2 py-0.5">
          <Sparkles className="w-3 h-3 mr-1" />
          AI
        </Badge>
      </div>
    </Link>
  )

  const DesktopNavigation = () => (
    <div className="hidden md:flex items-center gap-8">
      {isLandingPage && navigationLinks.map((link) => (
        <Link
          key={link.name}
          href={link.href}
          className="text-muted-foreground hover:text-foreground transition-colors relative group"
        >
          {link.name}
          <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-400 to-pink-400 group-hover:w-full transition-all duration-300" />
        </Link>
      ))}
    </div>
  )

  const AuthSection = () => {
    if (session) {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-9 w-9 rounded-full">
              <Avatar className="h-9 w-9 border-2 border-primary/20">
                <AvatarImage src={session.user?.image || ''} alt="Profile" />
                <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white">
                  {session.user?.name?.[0] || 'U'}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="flex items-center justify-start gap-2 p-2">
              <div className="flex flex-col space-y-1 leading-none">
                {session.user?.name && (
                  <p className="font-medium">{session.user.name}</p>
                )}
                {session.user?.email && (
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {session.user.email}
                  </p>
                )}
              </div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push('/profile')} className="cursor-pointer">
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push('/pricing')} className="cursor-pointer">
              <CreditCard className="mr-2 h-4 w-4" />
              Billing & Plans
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => signOut()} className="cursor-pointer text-red-600 focus:text-red-600">
              <LogOut className="mr-2 h-4 w-4" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }

    return (
      <div className="flex items-center gap-2">
        <Button 
          onClick={handleAuthAction}
          variant="ghost" 
          size="sm"
          className="text-muted-foreground hover:text-foreground"
        >
          Sign In
        </Button>
        <Button 
          onClick={handleAuthAction}
          size="sm"
          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
        >
          Get Started
        </Button>
      </div>
    )
  }

  const MobileMenu = () => (
    <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="md:hidden">
          <Menu className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-80">
        <div className="flex flex-col gap-6 mt-6">
          {isLandingPage && (
            <div className="flex flex-col gap-4">
              {navigationLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  onClick={() => setMobileMenuOpen(false)}
                  className="text-lg font-medium text-muted-foreground hover:text-foreground transition-colors"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          )}
          
          {session ? (
            <div className="flex flex-col gap-4 pt-4 border-t">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={session.user?.image || ''} alt="Profile" />
                  <AvatarFallback>{session.user?.name?.[0] || 'U'}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{session.user?.name}</p>
                  <p className="text-sm text-muted-foreground">{session.user?.email}</p>
                </div>
              </div>
              <Button 
                variant="outline" 
                onClick={() => {
                  router.push('/profile')
                  setMobileMenuOpen(false)
                }}
                className="justify-start"
              >
                <User className="mr-2 h-4 w-4" />
                Profile
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  router.push('/pricing')
                  setMobileMenuOpen(false)
                }}
                className="justify-start"
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Billing & Plans
              </Button>
              <Button 
                variant="outline" 
                onClick={() => signOut()}
                className="justify-start text-red-600 hover:text-red-600"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sign out
              </Button>
            </div>
          ) : (
            <div className="flex flex-col gap-3 pt-4 border-t">
              <Button onClick={handleAuthAction} variant="outline" className="w-full">
                Sign In
              </Button>
              <Button 
                onClick={handleAuthAction}
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              >
                Get Started
              </Button>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  )

  // Different styling for landing page vs app
  const containerClasses = isLandingPage 
    ? "border-b border-gray-800/50 bg-black/80 backdrop-blur-xl sticky top-0 z-50"
    : "sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"

  return (
    <header className={containerClasses}>
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4">
        <div className="flex items-center gap-4">
          {session && !isMobile && isWorkspacePage && (
            <>
              <SidebarTrigger 
                title="Toggle Sidebar" 
                className="h-9 w-9 hover:bg-accent hover:text-accent-foreground" 
              />
              <Separator orientation="vertical" className="h-6" />
            </>
          )}
          <Logo />
        </div>

        <DesktopNavigation />

        <div className="flex items-center gap-3">
          <div className="hidden md:flex">
            <AuthSection />
          </div>
          <MobileMenu />
        </div>
      </div>
    </header>
  )
}

export default Navbar
