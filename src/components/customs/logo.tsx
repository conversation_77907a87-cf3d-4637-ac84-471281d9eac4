import { Zap } from "lucide-react";
import React from "react";

const HateableLogo = () => {
  return (
    <div className="relative">
      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 via-pink-500 to-cyan-500 rounded-xl flex items-center justify-center transform rotate-12 group-hover:rotate-0 transition-transform duration-300">
        <Zap className="w-6 h-6 text-white" />
      </div>
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping" />
    </div>
  );
};

export default HateableLogo;
