import { ShadcnComponentDetector } from './shadcn-detector';

// Test code that should trigger shadcn component detection
const testTodoAppCode = `
import React, { useState } from 'react';

export default function TodoApp() {
  const [todos, setTodos] = useState([]);
  const [inputValue, setInputValue] = useState('');

  const addTodo = () => {
    if (inputValue.trim()) {
      setTodos([...todos, { id: Date.now(), text: inputValue, completed: false }]);
      setInputValue('');
    }
  };

  const toggleTodo = (id) => {
    setTodos(todos.map(todo => 
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const deleteTodo = (id) => {
    setTodos(todos.filter(todo => todo.id !== id));
  };

  return (
    <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold text-center mb-6">Todo App</h1>
      
      <div className="flex gap-2 mb-4">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Add a new todo..."
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={addTodo}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Add
        </button>
      </div>

      <div className="space-y-2">
        {todos.map(todo => (
          <div key={todo.id} className="flex items-center gap-2 p-3 border border-gray-200 rounded-md">
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
              className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
            />
            <span className={todo.completed ? 'line-through text-gray-500' : 'text-gray-900'}>
              {todo.text}
            </span>
            <button
              onClick={() => deleteTodo(todo.id)}
              className="ml-auto px-2 py-1 text-red-600 hover:bg-red-50 rounded"
            >
              Delete
            </button>
          </div>
        ))}
      </div>

      {todos.length === 0 && (
        <div className="text-center text-gray-500 mt-4">
          No todos yet. Add one above!
        </div>
      )}
    </div>
  );
}
`;

// Test the detection system
export function testShadcnDetection() {
  console.log('🧪 Testing shadcn component detection...');
  
  const result = ShadcnComponentDetector.detectComponents(testTodoAppCode);
  
  console.log('📊 Detection Results:');
  console.log('Detected Components:', result.detectedComponents.map(c => c.name));
  console.log('Required Imports:', result.requiredImports);
  console.log('Required Dependencies:', Object.keys(result.requiredDependencies));
  
  // Expected components for a todo app
  const expectedComponents = ['Button', 'Input', 'Card', 'Checkbox'];
  const detectedNames = result.detectedComponents.map(c => c.name);
  
  console.log('\n✅ Expected vs Detected:');
  expectedComponents.forEach(expected => {
    const detected = detectedNames.includes(expected);
    console.log(`${detected ? '✅' : '❌'} ${expected}: ${detected ? 'DETECTED' : 'NOT DETECTED'}`);
  });
  
  // Test component file generation
  const componentFiles = ShadcnComponentDetector.generateComponentFiles(result.detectedComponents);
  console.log('\n📁 Generated Component Files:', Object.keys(componentFiles));
  
  // Test utils file generation
  const utilsFiles = ShadcnComponentDetector.generateUtilsFile();
  console.log('📁 Generated Utils Files:', Object.keys(utilsFiles));
  
  return {
    success: result.detectedComponents.length > 0,
    detectedComponents: result.detectedComponents,
    requiredImports: result.requiredImports,
    requiredDependencies: result.requiredDependencies,
    componentFiles,
    utilsFiles
  };
}

// Example of how the enhanced code should look with shadcn components
export const expectedShadcnTodoCode = `
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

export default function TodoApp() {
  const [todos, setTodos] = useState([]);
  const [inputValue, setInputValue] = useState('');

  const addTodo = () => {
    if (inputValue.trim()) {
      setTodos([...todos, { id: Date.now(), text: inputValue, completed: false }]);
      setInputValue('');
    }
  };

  const toggleTodo = (id) => {
    setTodos(todos.map(todo => 
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const deleteTodo = (id) => {
    setTodos(todos.filter(todo => todo.id !== id));
  };

  return (
    <div className="max-w-md mx-auto mt-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-center">Todo App 📝</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Add a new todo..."
              className="flex-1"
            />
            <Button onClick={addTodo}>
              Add
            </Button>
          </div>

          <div className="space-y-2">
            {todos.map(todo => (
              <Card key={todo.id} className="p-3">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={todo.completed}
                    onCheckedChange={() => toggleTodo(todo.id)}
                  />
                  <span className={cn(
                    "flex-1",
                    todo.completed && "line-through text-muted-foreground"
                  )}>
                    {todo.text}
                  </span>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => deleteTodo(todo.id)}
                  >
                    Delete
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {todos.length === 0 && (
            <div className="text-center text-muted-foreground">
              No todos yet. Add one above! 🎯
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
`;

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  // Only run in Node.js environment
  try {
    const testResult = testShadcnDetection();
    console.log('\n🎉 Test completed!', testResult.success ? 'SUCCESS' : 'FAILED');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}
