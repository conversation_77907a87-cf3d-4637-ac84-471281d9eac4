/**
 * Incremental Code Editor Service
 * Handles intelligent code editing that preserves existing functionality
 * while adding new features or making targeted modifications
 */

export interface IncrementalEditRequest {
  prompt: string;
  existingFiles: { [path: string]: { code: string } };
  editType: 'feature' | 'bugfix' | 'refactor' | 'style' | 'component';
}

export interface IncrementalEditResponse {
  editType: string;
  summary: string;
  newFiles: { [path: string]: { code: string; reason: string } };
  modifiedFiles: { [path: string]: { code: string; changes: string; reason: string } };
  unchangedFiles: string[];
  usageInstructions: string;
  usedShadcnComponents: string[];
  detectedShadcnComponents?: any[];
  requiredShadcnImports?: string[];
}

export class IncrementalEditor {
  /**
   * Determines if a request should use incremental editing or full regeneration
   */
  static shouldUseIncrementalEdit(prompt: string, existingFiles: any): boolean {
    const incrementalKeywords = [
      'add', 'create', 'implement', 'include', 'insert',
      'modify', 'update', 'change', 'edit', 'fix',
      'improve', 'enhance', 'extend', 'append',
      'feature', 'functionality', 'component', 'button',
      'form', 'page', 'section', 'modal', 'dialog'
    ];

    const fullRewriteKeywords = [
      'rebuild', 'recreate', 'start over', 'from scratch',
      'completely new', 'rewrite everything', 'new project',
      'different approach', 'change everything'
    ];

    const promptLower = prompt.toLowerCase();
    
    // Check for full rewrite indicators
    const hasFullRewriteKeywords = fullRewriteKeywords.some(keyword => 
      promptLower.includes(keyword)
    );
    
    if (hasFullRewriteKeywords) {
      return false;
    }

    // Check for incremental edit indicators
    const hasIncrementalKeywords = incrementalKeywords.some(keyword => 
      promptLower.includes(keyword)
    );

    // If we have existing files and incremental keywords, use incremental editing
    const hasExistingFiles = existingFiles && Object.keys(existingFiles).length > 0;
    
    return hasExistingFiles && hasIncrementalKeywords;
  }

  /**
   * Analyzes the prompt to determine the type of edit being requested
   */
  static determineEditType(prompt: string): string {
    const promptLower = prompt.toLowerCase();

    if (promptLower.includes('bug') || promptLower.includes('fix') || promptLower.includes('error')) {
      return 'bugfix';
    }
    
    if (promptLower.includes('style') || promptLower.includes('design') || promptLower.includes('css') || promptLower.includes('color')) {
      return 'style';
    }
    
    if (promptLower.includes('refactor') || promptLower.includes('reorganize') || promptLower.includes('clean up')) {
      return 'refactor';
    }
    
    if (promptLower.includes('component') || promptLower.includes('widget') || promptLower.includes('element')) {
      return 'component';
    }

    // Default to feature for most requests
    return 'feature';
  }

  /**
   * Merges incremental edit results with existing files
   */
  static mergeEditResults(
    existingFiles: { [path: string]: { code: string } },
    editResponse: IncrementalEditResponse
  ): { [path: string]: { code: string } } {
    const mergedFiles = { ...existingFiles };

    // Add new files
    Object.entries(editResponse.newFiles || {}).forEach(([path, file]) => {
      mergedFiles[path] = { code: file.code };
    });

    // Update modified files
    Object.entries(editResponse.modifiedFiles || {}).forEach(([path, file]) => {
      mergedFiles[path] = { code: file.code };
    });

    return mergedFiles;
  }

  /**
   * Generates a summary of changes for user feedback
   */
  static generateChangeSummary(editResponse: IncrementalEditResponse): string {
    const newFileCount = Object.keys(editResponse.newFiles || {}).length;
    const modifiedFileCount = Object.keys(editResponse.modifiedFiles || {}).length;
    const unchangedFileCount = editResponse.unchangedFiles?.length || 0;

    let summary = editResponse.summary || 'Code updated successfully';
    
    if (newFileCount > 0) {
      summary += `\n\n📁 New files created: ${newFileCount}`;
      Object.entries(editResponse.newFiles || {}).forEach(([path, file]) => {
        summary += `\n  • ${path} - ${file.reason}`;
      });
    }

    if (modifiedFileCount > 0) {
      summary += `\n\n✏️ Files modified: ${modifiedFileCount}`;
      Object.entries(editResponse.modifiedFiles || {}).forEach(([path, file]) => {
        summary += `\n  • ${path} - ${file.changes}`;
      });
    }

    if (unchangedFileCount > 0) {
      summary += `\n\n✅ Files preserved: ${unchangedFileCount}`;
    }

    if (editResponse.usedShadcnComponents?.length > 0) {
      summary += `\n\n🎨 shadcn/ui components used: ${editResponse.usedShadcnComponents.join(', ')}`;
    }

    if (editResponse.usageInstructions) {
      summary += `\n\n💡 Usage: ${editResponse.usageInstructions}`;
    }

    return summary;
  }

  /**
   * Validates that the edit response has the correct structure
   */
  static validateEditResponse(response: any): boolean {
    if (!response || typeof response !== 'object') {
      return false;
    }

    // Check required fields
    const requiredFields = ['editType', 'summary'];
    for (const field of requiredFields) {
      if (!(field in response)) {
        console.warn(`Missing required field: ${field}`);
        return false;
      }
    }

    // Ensure at least one of newFiles or modifiedFiles exists
    if (!response.newFiles && !response.modifiedFiles) {
      console.warn('No new or modified files in edit response');
      return false;
    }

    return true;
  }
}
