import Lookup from '@/data/Lookup';

export interface DetectedComponent {
  name: string;
  import: string;
  subComponents?: string[];
  usageCount: number;
  variants?: string[];
  sizes?: string[];
}

export interface ComponentDetectionResult {
  detectedComponents: DetectedComponent[];
  requiredImports: string[];
  requiredDependencies: { [key: string]: string };
}

/**
 * Analyzes generated code to detect UI patterns that can be replaced with shadcn components
 */
export class ShadcnComponentDetector {
  private static readonly UI_PATTERNS = {
    // Button patterns
    button: [
      /\<button[^>]*className[^>]*\>/gi,
      /\<button[^>]*class[^>]*\>/gi,
      /onClick.*button/gi,
      /type=['"]button['"]|type=['"]submit['"]|type=['"]reset['"]/gi
    ],
    
    // Input patterns
    input: [
      /\<input[^>]*type=['"]text['"][^>]*\>/gi,
      /\<input[^>]*type=['"]email['"][^>]*\>/gi,
      /\<input[^>]*type=['"]password['"][^>]*\>/gi,
      /\<input[^>]*type=['"]number['"][^>]*\>/gi,
      /\<input[^>]*placeholder[^>]*\>/gi
    ],
    
    // Card patterns
    card: [
      /className[^>]*['"].*card.*['"][^>]*\>/gi,
      /className[^>]*['"].*border.*rounded.*['"][^>]*\>/gi,
      /className[^>]*['"].*shadow.*['"][^>]*\>/gi,
      /\<div[^>]*className[^>]*['"].*bg-white.*border.*['"][^>]*\>/gi
    ],
    
    // Form patterns
    form: [
      /\<form[^>]*\>/gi,
      /\<label[^>]*htmlFor[^>]*\>/gi,
      /\<label[^>]*for[^>]*\>/gi
    ],
    
    // Select patterns
    select: [
      /\<select[^>]*\>/gi,
      /\<option[^>]*\>/gi
    ],
    
    // Checkbox patterns
    checkbox: [
      /\<input[^>]*type=['"]checkbox['"][^>]*\>/gi,
      /type=['"]checkbox['"]/gi
    ],
    
    // Radio patterns
    radio: [
      /\<input[^>]*type=['"]radio['"][^>]*\>/gi,
      /type=['"]radio['"]/gi
    ],
    
    // Textarea patterns
    textarea: [
      /\<textarea[^>]*\>/gi
    ],
    
    // Dialog/Modal patterns
    dialog: [
      /className[^>]*['"].*modal.*['"][^>]*\>/gi,
      /className[^>]*['"].*dialog.*['"][^>]*\>/gi,
      /className[^>]*['"].*overlay.*['"][^>]*\>/gi,
      /className[^>]*['"].*backdrop.*['"][^>]*\>/gi
    ],
    
    // Alert patterns
    alert: [
      /className[^>]*['"].*alert.*['"][^>]*\>/gi,
      /className[^>]*['"].*notification.*['"][^>]*\>/gi,
      /className[^>]*['"].*warning.*['"][^>]*\>/gi,
      /className[^>]*['"].*error.*['"][^>]*\>/gi,
      /className[^>]*['"].*success.*['"][^>]*\>/gi
    ],
    
    // Badge patterns
    badge: [
      /className[^>]*['"].*badge.*['"][^>]*\>/gi,
      /className[^>]*['"].*tag.*['"][^>]*\>/gi,
      /className[^>]*['"].*chip.*['"][^>]*\>/gi
    ],
    
    // Table patterns
    table: [
      /\<table[^>]*\>/gi,
      /\<thead[^>]*\>/gi,
      /\<tbody[^>]*\>/gi,
      /\<tr[^>]*\>/gi,
      /\<th[^>]*\>/gi,
      /\<td[^>]*\>/gi
    ],
    
    // Tab patterns
    tabs: [
      /className[^>]*['"].*tab.*['"][^>]*\>/gi,
      /role=['"]tab['"]|role=['"]tablist['"]|role=['"]tabpanel['"]/gi
    ],
    
    // Progress patterns
    progress: [
      /\<progress[^>]*\>/gi,
      /className[^>]*['"].*progress.*['"][^>]*\>/gi,
      /className[^>]*['"].*loading.*['"][^>]*\>/gi
    ],
    
    // Avatar patterns
    avatar: [
      /className[^>]*['"].*avatar.*['"][^>]*\>/gi,
      /className[^>]*['"].*profile.*image.*['"][^>]*\>/gi,
      /className[^>]*['"].*rounded-full.*['"][^>]*\>/gi
    ],
    
    // Skeleton patterns
    skeleton: [
      /className[^>]*['"].*skeleton.*['"][^>]*\>/gi,
      /className[^>]*['"].*loading.*placeholder.*['"][^>]*\>/gi,
      /className[^>]*['"].*animate-pulse.*['"][^>]*\>/gi
    ]
  };

  /**
   * Detects shadcn components that should be used based on code patterns
   */
  static detectComponents(code: string): ComponentDetectionResult {
    const detectedComponents: DetectedComponent[] = [];
    const requiredImports: string[] = [];
    const requiredDependencies: { [key: string]: string } = {};

    // Analyze code for each component pattern
    Object.entries(this.UI_PATTERNS).forEach(([componentType, patterns]) => {
      let usageCount = 0;
      
      patterns.forEach(pattern => {
        const matches = code.match(pattern);
        if (matches) {
          usageCount += matches.length;
        }
      });

      if (usageCount > 0) {
        const shadcnComponent = this.mapToShadcnComponent(componentType, usageCount);
        if (shadcnComponent) {
          detectedComponents.push(shadcnComponent);
          requiredImports.push(shadcnComponent.import);
          
          // Add required dependencies
          this.addRequiredDependencies(componentType, requiredDependencies);
        }
      }
    });

    return {
      detectedComponents,
      requiredImports,
      requiredDependencies
    };
  }

  /**
   * Maps detected UI patterns to appropriate shadcn components
   */
  private static mapToShadcnComponent(componentType: string, usageCount: number): DetectedComponent | null {
    const componentMap: { [key: string]: string } = {
      button: 'Button',
      input: 'Input',
      card: 'Card',
      form: 'Label',
      select: 'Select',
      checkbox: 'Checkbox',
      radio: 'RadioGroup',
      textarea: 'Textarea',
      dialog: 'Dialog',
      alert: 'Alert',
      badge: 'Badge',
      table: 'Table',
      tabs: 'Tabs',
      progress: 'Progress',
      avatar: 'Avatar',
      skeleton: 'Skeleton'
    };

    const shadcnComponentName = componentMap[componentType];
    if (!shadcnComponentName) return null;

    const componentInfo = Lookup.SHADCN_COMPONENTS[shadcnComponentName];
    if (!componentInfo) return null;

    return {
      name: shadcnComponentName,
      import: componentInfo.import,
      subComponents: componentInfo.subComponents,
      usageCount,
      variants: componentInfo.variants,
      sizes: componentInfo.sizes
    };
  }

  /**
   * Adds required dependencies for specific component types
   */
  private static addRequiredDependencies(componentType: string, dependencies: { [key: string]: string }): void {
    const dependencyMap: { [key: string]: string[] } = {
      button: ['@radix-ui/react-slot', 'class-variance-authority'],
      input: [],
      card: [],
      form: ['@radix-ui/react-label'],
      select: ['@radix-ui/react-select'],
      checkbox: ['@radix-ui/react-checkbox'],
      radio: ['@radix-ui/react-radio-group'],
      textarea: [],
      dialog: ['@radix-ui/react-dialog'],
      alert: [],
      badge: ['class-variance-authority'],
      table: [],
      tabs: ['@radix-ui/react-tabs'],
      progress: ['@radix-ui/react-progress'],
      avatar: ['@radix-ui/react-avatar'],
      skeleton: []
    };

    const requiredDeps = dependencyMap[componentType] || [];
    requiredDeps.forEach(dep => {
      if (Lookup.DEPENDENCY[dep]) {
        dependencies[dep] = Lookup.DEPENDENCY[dep];
      }
    });
  }

  /**
   * Generates component files with shadcn imports
   */
  static generateComponentFiles(detectedComponents: DetectedComponent[]): { [key: string]: { code: string } } {
    const files: { [key: string]: { code: string } } = {};

    detectedComponents.forEach(component => {
      const componentPath = `/components/ui/${component.name.toLowerCase()}.tsx`;
      
      // Check if we have the component in our ui folder
      const componentExists = this.checkComponentExists(component.name);
      if (componentExists) {
        // Add a reference file that imports from our ui folder
        files[componentPath] = {
          code: `// This component is available in the project
${component.import}

export { ${component.name}${component.subComponents ? `, ${component.subComponents.join(', ')}` : ''} };`
        };
      }
    });

    return files;
  }

  /**
   * Checks if a shadcn component exists in the project
   */
  private static checkComponentExists(componentName: string): boolean {
    // In a real implementation, this would check the file system
    // For now, we'll assume all components in SHADCN_COMPONENTS exist
    return !!Lookup.SHADCN_COMPONENTS[componentName];
  }

  /**
   * Generates utils file if needed
   */
  static generateUtilsFile(): { [key: string]: { code: string } } {
    return {
      '/lib/utils.ts': {
        code: `import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}`
      }
    };
  }
}
