import Lookup from '@/data/Lookup';

export interface DetectedComponent {
  name: string;
  import: string;
  subComponents?: string[];
  usageCount: number;
  variants?: string[];
  sizes?: string[];
}

export interface ComponentDetectionResult {
  detectedComponents: DetectedComponent[];
  requiredImports: string[];
  requiredDependencies: { [key: string]: string };
}

/**
 * Analyzes generated code to detect UI patterns that can be replaced with shadcn components
 */
export class ShadcnComponentDetector {
  /**
   * Creates component detection result from AI-specified components
   * This is more reliable than pattern detection
   */
  static createFromAIComponents(usedComponents: string[]): ComponentDetectionResult {
    const detectedComponents: DetectedComponent[] = [];
    const requiredImports: string[] = [];
    const requiredDependencies: { [key: string]: string } = {};

    usedComponents.forEach(componentName => {
      const componentInfo = (Lookup.SHADCN_COMPONENTS as any)[componentName];
      if (componentInfo) {
        const detectedComponent: DetectedComponent = {
          name: componentName,
          import: componentInfo.import,
          subComponents: componentInfo.subComponents,
          usageCount: 1,
          variants: componentInfo.variants,
          sizes: componentInfo.sizes
        };

        detectedComponents.push(detectedComponent);
        requiredImports.push(componentInfo.import);

        // Add required dependencies for this component
        this.addRequiredDependenciesForComponent(componentName, requiredDependencies);
      }
    });

    return {
      detectedComponents,
      requiredImports,
      requiredDependencies
    };
  }

  /**
   * Adds required dependencies for a specific component
   */
  private static addRequiredDependenciesForComponent(componentName: string, dependencies: { [key: string]: string }) {
    // Map components to their required dependencies
    const componentDependencies: { [key: string]: string[] } = {
      'AlertDialog': ['@radix-ui/react-alert-dialog'],
      'Avatar': ['@radix-ui/react-avatar'],
      'Checkbox': ['@radix-ui/react-checkbox'],
      'Collapsible': ['@radix-ui/react-collapsible'],
      'Command': ['cmdk'],
      'ContextMenu': ['@radix-ui/react-context-menu'],
      'Dialog': ['@radix-ui/react-dialog'],
      'Drawer': ['vaul'],
      'DropdownMenu': ['@radix-ui/react-dropdown-menu'],
      'Form': ['@hookform/resolvers', 'react-hook-form', 'zod'],
      'HoverCard': ['@radix-ui/react-hover-card'],
      'Label': ['@radix-ui/react-label'],
      'Menubar': ['@radix-ui/react-menubar'],
      'NavigationMenu': ['@radix-ui/react-navigation-menu'],
      'Popover': ['@radix-ui/react-popover'],
      'Progress': ['@radix-ui/react-progress'],
      'RadioGroup': ['@radix-ui/react-radio-group'],
      'ScrollArea': ['@radix-ui/react-scroll-area'],
      'Select': ['@radix-ui/react-select'],
      'Separator': ['@radix-ui/react-separator'],
      'Sheet': ['@radix-ui/react-dialog'],
      'Slider': ['@radix-ui/react-slider'],
      'Switch': ['@radix-ui/react-switch'],
      'Tabs': ['@radix-ui/react-tabs'],
      'Toast': ['@radix-ui/react-toast'],
      'Toggle': ['@radix-ui/react-toggle'],
      'ToggleGroup': ['@radix-ui/react-toggle-group'],
      'Tooltip': ['@radix-ui/react-tooltip']
    };

    const requiredDeps = componentDependencies[componentName] || [];
    requiredDeps.forEach(dep => {
      if ((Lookup.DEPENDENCY as any)[dep]) {
        dependencies[dep] = (Lookup.DEPENDENCY as any)[dep];
      }
    });
  }
  private static readonly UI_PATTERNS = {
    // Button patterns
    button: [
      /\<button[^>]*className[^>]*\>/gi,
      /\<button[^>]*class[^>]*\>/gi,
      /onClick.*button/gi,
      /type=['"]button['"]|type=['"]submit['"]|type=['"]reset['"]/gi,
      /Button/gi,
      /\<Button[^>]*\>/gi
    ],

    // Input patterns
    input: [
      /\<input[^>]*type=['"]text['"][^>]*\>/gi,
      /\<input[^>]*type=['"]email['"][^>]*\>/gi,
      /\<input[^>]*type=['"]password['"][^>]*\>/gi,
      /\<input[^>]*type=['"]number['"][^>]*\>/gi,
      /\<input[^>]*placeholder[^>]*\>/gi,
      /Input/gi,
      /\<Input[^>]*\>/gi
    ],

    // Checkbox patterns - ENHANCED
    checkboxEnhanced: [
      /\<input[^>]*type=['"]checkbox['"][^>]*\>/gi,
      /checkbox/gi,
      /Checkbox/gi,
      /\<Checkbox[^>]*\>/gi,
      /checked.*onChange/gi,
      /type=['"]checkbox['"]/gi
    ],

    // Dialog patterns - ENHANCED
    dialogEnhanced: [
      /dialog/gi,
      /Dialog/gi,
      /\<Dialog[^>]*\>/gi,
      /modal/gi,
      /Modal/gi,
      /popup/gi,
      /overlay/gi,
      /DialogContent/gi,
      /DialogHeader/gi,
      /DialogTitle/gi,
      /DialogDescription/gi,
      /DialogFooter/gi,
      /DialogTrigger/gi,
      /className[^>]*['"].*modal.*['"][^>]*\>/gi,
      /className[^>]*['"].*dialog.*['"][^>]*\>/gi,
      /className[^>]*['"].*overlay.*['"][^>]*\>/gi,
      /className[^>]*['"].*backdrop.*['"][^>]*\>/gi
    ],

    // Alert Dialog patterns
    alertDialog: [
      /AlertDialog/gi,
      /\<AlertDialog[^>]*\>/gi,
      /AlertDialogContent/gi,
      /AlertDialogHeader/gi,
      /AlertDialogTitle/gi,
      /AlertDialogDescription/gi,
      /AlertDialogFooter/gi,
      /AlertDialogTrigger/gi,
      /AlertDialogAction/gi,
      /AlertDialogCancel/gi,
      /confirm.*dialog/gi,
      /delete.*confirm/gi
    ],
    
    // Card patterns
    card: [
      /className[^>]*['"].*card.*['"][^>]*\>/gi,
      /className[^>]*['"].*border.*rounded.*['"][^>]*\>/gi,
      /className[^>]*['"].*shadow.*['"][^>]*\>/gi,
      /\<div[^>]*className[^>]*['"].*bg-white.*border.*['"][^>]*\>/gi
    ],
    
    // Form patterns
    form: [
      /\<form[^>]*\>/gi,
      /\<label[^>]*htmlFor[^>]*\>/gi,
      /\<label[^>]*for[^>]*\>/gi
    ],
    
    // Select patterns
    select: [
      /\<select[^>]*\>/gi,
      /\<option[^>]*\>/gi
    ],
    
    // Checkbox patterns
    checkbox: [
      /\<input[^>]*type=['"]checkbox['"][^>]*\>/gi,
      /type=['"]checkbox['"]/gi
    ],
    
    // Radio patterns
    radio: [
      /\<input[^>]*type=['"]radio['"][^>]*\>/gi,
      /type=['"]radio['"]/gi
    ],
    
    // Textarea patterns
    textarea: [
      /\<textarea[^>]*\>/gi
    ],
    
    // Dialog/Modal patterns
    dialog: [
      /className[^>]*['"].*modal.*['"][^>]*\>/gi,
      /className[^>]*['"].*dialog.*['"][^>]*\>/gi,
      /className[^>]*['"].*overlay.*['"][^>]*\>/gi,
      /className[^>]*['"].*backdrop.*['"][^>]*\>/gi
    ],
    
    // Alert patterns
    alert: [
      /className[^>]*['"].*alert.*['"][^>]*\>/gi,
      /className[^>]*['"].*notification.*['"][^>]*\>/gi,
      /className[^>]*['"].*warning.*['"][^>]*\>/gi,
      /className[^>]*['"].*error.*['"][^>]*\>/gi,
      /className[^>]*['"].*success.*['"][^>]*\>/gi
    ],
    
    // Badge patterns
    badge: [
      /className[^>]*['"].*badge.*['"][^>]*\>/gi,
      /className[^>]*['"].*tag.*['"][^>]*\>/gi,
      /className[^>]*['"].*chip.*['"][^>]*\>/gi
    ],
    
    // Table patterns
    table: [
      /\<table[^>]*\>/gi,
      /\<thead[^>]*\>/gi,
      /\<tbody[^>]*\>/gi,
      /\<tr[^>]*\>/gi,
      /\<th[^>]*\>/gi,
      /\<td[^>]*\>/gi
    ],
    
    // Tab patterns
    tabs: [
      /className[^>]*['"].*tab.*['"][^>]*\>/gi,
      /role=['"]tab['"]|role=['"]tablist['"]|role=['"]tabpanel['"]/gi
    ],
    
    // Progress patterns
    progress: [
      /\<progress[^>]*\>/gi,
      /className[^>]*['"].*progress.*['"][^>]*\>/gi,
      /className[^>]*['"].*loading.*['"][^>]*\>/gi
    ],
    
    // Avatar patterns
    avatar: [
      /className[^>]*['"].*avatar.*['"][^>]*\>/gi,
      /className[^>]*['"].*profile.*image.*['"][^>]*\>/gi,
      /className[^>]*['"].*rounded-full.*['"][^>]*\>/gi
    ],
    
    // Skeleton patterns
    skeleton: [
      /className[^>]*['"].*skeleton.*['"][^>]*\>/gi,
      /className[^>]*['"].*loading.*placeholder.*['"][^>]*\>/gi,
      /className[^>]*['"].*animate-pulse.*['"][^>]*\>/gi
    ]
  };

  /**
   * Detects shadcn components that should be used based on code patterns
   */
  static detectComponents(code: string): ComponentDetectionResult {
    const detectedComponents: DetectedComponent[] = [];
    const requiredImports: string[] = [];
    const requiredDependencies: { [key: string]: string } = {};

    // Analyze code for each component pattern
    Object.entries(this.UI_PATTERNS).forEach(([componentType, patterns]) => {
      let usageCount = 0;
      
      patterns.forEach(pattern => {
        const matches = code.match(pattern);
        if (matches) {
          usageCount += matches.length;
        }
      });

      if (usageCount > 0) {
        const shadcnComponent = this.mapToShadcnComponent(componentType, usageCount);
        if (shadcnComponent) {
          detectedComponents.push(shadcnComponent);
          requiredImports.push(shadcnComponent.import);
          
          // Add required dependencies
          this.addRequiredDependencies(componentType, requiredDependencies);
        }
      }
    });

    return {
      detectedComponents,
      requiredImports,
      requiredDependencies
    };
  }

  /**
   * Maps detected UI patterns to appropriate shadcn components
   */
  private static mapToShadcnComponent(componentType: string, usageCount: number): DetectedComponent | null {
    const componentMap: { [key: string]: string } = {
      button: 'Button',
      input: 'Input',
      card: 'Card',
      form: 'Label',
      select: 'Select',
      checkbox: 'Checkbox',
      radio: 'RadioGroup',
      textarea: 'Textarea',
      dialog: 'Dialog',
      alert: 'Alert',
      badge: 'Badge',
      table: 'Table',
      tabs: 'Tabs',
      progress: 'Progress',
      avatar: 'Avatar',
      skeleton: 'Skeleton'
    };

    const shadcnComponentName = componentMap[componentType];
    if (!shadcnComponentName) return null;

    const componentInfo = Lookup.SHADCN_COMPONENTS[shadcnComponentName];
    if (!componentInfo) return null;

    return {
      name: shadcnComponentName,
      import: componentInfo.import,
      subComponents: componentInfo.subComponents,
      usageCount,
      variants: componentInfo.variants,
      sizes: componentInfo.sizes
    };
  }

  /**
   * Adds required dependencies for specific component types
   */
  private static addRequiredDependencies(componentType: string, dependencies: { [key: string]: string }): void {
    const dependencyMap: { [key: string]: string[] } = {
      button: ['@radix-ui/react-slot', 'class-variance-authority'],
      input: [],
      card: [],
      form: ['@radix-ui/react-label'],
      select: ['@radix-ui/react-select'],
      checkbox: ['@radix-ui/react-checkbox'],
      radio: ['@radix-ui/react-radio-group'],
      textarea: [],
      dialog: ['@radix-ui/react-dialog'],
      alert: [],
      badge: ['class-variance-authority'],
      table: [],
      tabs: ['@radix-ui/react-tabs'],
      progress: ['@radix-ui/react-progress'],
      avatar: ['@radix-ui/react-avatar'],
      skeleton: []
    };

    const requiredDeps = dependencyMap[componentType] || [];
    requiredDeps.forEach(dep => {
      if (Lookup.DEPENDENCY[dep]) {
        dependencies[dep] = Lookup.DEPENDENCY[dep];
      }
    });
  }

  /**
   * Generates component files with full shadcn component code
   */
  static generateComponentFiles(detectedComponents: DetectedComponent[]): { [key: string]: { code: string } } {
    const files: { [key: string]: { code: string } } = {};

    detectedComponents.forEach(component => {
      const componentPath = `/components/ui/${component.name.toLowerCase()}.tsx`;

      // Check if we have the component in our ui folder
      const componentExists = this.checkComponentExists(component.name);
      if (componentExists) {
        // Get the full component code instead of just import reference
        const fullComponentCode = this.getFullComponentCode(component.name);
        if (fullComponentCode) {
          files[componentPath] = {
            code: fullComponentCode
          };
        }
      }
    });

    return files;
  }

  /**
   * Checks if a shadcn component exists in the project
   */
  private static checkComponentExists(componentName: string): boolean {
    // In a real implementation, this would check the file system
    // For now, we'll assume all components in SHADCN_COMPONENTS exist
    return !!Lookup.SHADCN_COMPONENTS[componentName];
  }

  /**
   * Gets the full component code for a shadcn component
   */
  private static getFullComponentCode(componentName: string): string | null {
    // Map of component names to their full implementation
    const componentImplementations: { [key: string]: string } = {
      "Input": `import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className
      )}
      {...props}
    />
  )
}

export { Input }`,

      "Button": `import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }`,

      "Card": `import * as React from "react"

import { cn } from "@/lib/utils"

function Card({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card"
      className={cn(
        "bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",
        className
      )}
      {...props}
    />
  )
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",
        className
      )}
      {...props}
    />
  )
}

function CardTitle({ className, ...props }: React.ComponentProps<"h3">) {
  return (
    <h3
      data-slot="card-title"
      className={cn("font-semibold leading-none tracking-tight", className)}
      {...props}
    />
  )
}

function CardDescription({ className, ...props }: React.ComponentProps<"p">) {
  return (
    <p
      data-slot="card-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

function CardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div data-slot="card-content" className={cn("px-6", className)} {...props} />
  )
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-footer"
      className={cn("flex items-center px-6", className)}
      {...props}
    />
  )
}

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }`,

      "Label": `import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
)

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
))
Label.displayName = LabelPrimitive.Root.displayName

export { Label }`,

      "Textarea": `import * as React from "react"

import { cn } from "@/lib/utils"

function Textarea({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        "placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground border-input flex min-h-[60px] w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className
      )}
      {...props}
    />
  )
}

export { Textarea }`,

      "Badge": `import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }`,

      "Alert": `import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }`
    };

    return componentImplementations[componentName] || null;
  }

  /**
   * Generates utils file if needed
   */
  static generateUtilsFile(): { [key: string]: { code: string } } {
    return {
      '/lib/utils.ts': {
        code: `import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}`
      }
    };
  }

  /**
   * Generates TypeScript configuration file for proper path resolution
   */
  static generateTsConfigFile(): { [key: string]: { code: string } } {
    return {
      '/tsconfig.json': {
        code: `{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules"]
}`
      }
    };
  }
}
