

export default {
  SUGGESTIONS: ['Create ToDo App in React', 'Create Budget Track App', 'Create Gym Management Portal Dashboard', 'Create Quiz App On History', 'Create Login Signup Screen'],
  HERO_HEADING: 'What do you want to build?',
  HERO_DESC: 'Prompt, run, edit, and deploy full-stack web apps.',
  INPUT_PLACEHOLDER: 'What you want to build?',
  SIGNIN_HEADING: 'Continue With Hateable',
  SIGNIN_SUBHEADING: 'To use Hateable you must log into an existing account or create one.',
  SIGNIN_AGREEMENT_TEXT: 'By using Hateable, you agree to the collection of usage data for analytics.',

  DEFAULT_FILE: {
    '/public/index.html': {
      code: `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>`
    },
    '/App.css': {
      code: `
            @tailwind base;
@tailwind components;
@tailwind utilities;`
    },
    '/tailwind.config.js': {
      code: `
            /** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}", 
    "./public/index.html", 
    "./components/**/*.{js,jsx,ts,tsx}"
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`
    },
    '/postcss.config.js': {
      code: `/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {}
  },
};

export default config;
`
    }
  },
  DEPENDENCY: {
    "tailwindcss": "^3.4.1",
    "postcss": "^8.4.31",
    "autoprefixer": "^10.4.12",
    "@codesandbox/sandpack-react": "^2.20.0",
    "@google/generative-ai": "^0.22.0",
    "@radix-ui/react-dialog": "^1.1.6",
    "@radix-ui/react-slot": "^1.1.2",
    "@react-oauth/google": "^0.12.1",
    "@types/uuid4": "^2.0.3",
    "axios": "^1.7.9",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "convex": "^1.19.2",
    "dedent": "^1.5.3",
    "lucide-react": "^0.475.0",
    "next": "15.1.7",
    "next-themes": "^0.4.4",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "react-markdown": "^10.0.0",
    "tailwind-merge": "^3.0.2",
    "tailwindcss-animate": "^1.0.7",
    "uuid4": "^2.0.3",
    "react-chartjs-2": "^5.0.1",
    // shadcn/ui dependencies
    "@radix-ui/react-accordion": "^1.1.2",
    "@radix-ui/react-alert-dialog": "^1.0.5",
    "@radix-ui/react-aspect-ratio": "^1.0.3",
    "@radix-ui/react-avatar": "^1.0.4",
    "@radix-ui/react-checkbox": "^1.0.4",
    "@radix-ui/react-collapsible": "^1.0.3",
    "@radix-ui/react-context-menu": "^2.1.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-hover-card": "^1.0.7",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-menubar": "^1.0.4",
    "@radix-ui/react-navigation-menu": "^1.1.4",
    "@radix-ui/react-popover": "^1.0.7",
    "@radix-ui/react-progress": "^1.0.3",
    "@radix-ui/react-radio-group": "^1.1.3",
    "@radix-ui/react-scroll-area": "^1.0.5",
    "@radix-ui/react-select": "^2.0.0",
    "@radix-ui/react-separator": "^1.0.3",
    "@radix-ui/react-slider": "^1.1.2",
    "@radix-ui/react-switch": "^1.0.3",
    "@radix-ui/react-tabs": "^1.0.4",
    "@radix-ui/react-toggle": "^1.0.3",
    "@radix-ui/react-toggle-group": "^1.0.4",
    "@radix-ui/react-tooltip": "^1.0.7",
    "@hookform/resolvers": "^3.3.2",
    "react-hook-form": "^7.48.2",
    "react-day-picker": "^8.9.1",
    "date-fns": "^2.30.0",
    "cmdk": "^0.2.0",
    "embla-carousel-react": "^8.0.0",
    "recharts": "^2.8.0",
    "input-otp": "^1.2.4",
    "react-resizable-panels": "^0.0.55",
    "sonner": "^1.4.0",
    "vaul": "^0.9.0",
    "zod": "^3.22.4",
  },

  // Available shadcn components mapping
  SHADCN_COMPONENTS: {
    "Button": {
      import: "import { Button } from '@/components/ui/button'",
      usage: "<Button variant='default' size='default'>Click me</Button>",
      variants: ["default", "destructive", "outline", "secondary", "ghost", "link"],
      sizes: ["default", "sm", "lg", "icon"]
    },
    "Card": {
      import: "import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'",
      usage: "<Card><CardHeader><CardTitle>Title</CardTitle><CardDescription>Description</CardDescription></CardHeader><CardContent>Content</CardContent></Card>",
      subComponents: ["CardHeader", "CardTitle", "CardDescription", "CardContent", "CardFooter", "CardAction"]
    },
    "Input": {
      import: "import { Input } from '@/components/ui/input'",
      usage: "<Input type='text' placeholder='Enter text...' />",
      types: ["text", "email", "password", "number", "tel", "url", "search"]
    },
    "Label": {
      import: "import { Label } from '@/components/ui/label'",
      usage: "<Label htmlFor='input'>Label text</Label>"
    },
    "Textarea": {
      import: "import { Textarea } from '@/components/ui/textarea'",
      usage: "<Textarea placeholder='Enter your message...' />"
    },
    "Select": {
      import: "import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'",
      usage: "<Select><SelectTrigger><SelectValue placeholder='Select option' /></SelectTrigger><SelectContent><SelectItem value='option1'>Option 1</SelectItem></SelectContent></Select>",
      subComponents: ["SelectContent", "SelectItem", "SelectTrigger", "SelectValue"]
    },
    "Checkbox": {
      import: "import { Checkbox } from '@/components/ui/checkbox'",
      usage: "<Checkbox id='terms' />"
    },
    "RadioGroup": {
      import: "import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'",
      usage: "<RadioGroup><div className='flex items-center space-x-2'><RadioGroupItem value='option1' id='option1' /><Label htmlFor='option1'>Option 1</Label></div></RadioGroup>",
      subComponents: ["RadioGroupItem"]
    },
    "Switch": {
      import: "import { Switch } from '@/components/ui/switch'",
      usage: "<Switch />"
    },
    "Tabs": {
      import: "import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'",
      usage: "<Tabs defaultValue='tab1'><TabsList><TabsTrigger value='tab1'>Tab 1</TabsTrigger></TabsList><TabsContent value='tab1'>Content</TabsContent></Tabs>",
      subComponents: ["TabsContent", "TabsList", "TabsTrigger"]
    },
    "Dialog": {
      import: "import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'",
      usage: "<Dialog><DialogTrigger asChild><Button>Open</Button></DialogTrigger><DialogContent><DialogHeader><DialogTitle>Title</DialogTitle><DialogDescription>Description</DialogDescription></DialogHeader></DialogContent></Dialog>",
      subComponents: ["DialogContent", "DialogDescription", "DialogHeader", "DialogTitle", "DialogTrigger", "DialogFooter"]
    },
    "AlertDialog": {
      import: "import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'",
      usage: "<AlertDialog><AlertDialogTrigger asChild><Button>Delete</Button></AlertDialogTrigger><AlertDialogContent><AlertDialogHeader><AlertDialogTitle>Are you sure?</AlertDialogTitle><AlertDialogDescription>This action cannot be undone.</AlertDialogDescription></AlertDialogHeader><AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction>Continue</AlertDialogAction></AlertDialogFooter></AlertDialogContent></AlertDialog>",
      subComponents: ["AlertDialogAction", "AlertDialogCancel", "AlertDialogContent", "AlertDialogDescription", "AlertDialogFooter", "AlertDialogHeader", "AlertDialogTitle", "AlertDialogTrigger"]
    },
    "Sheet": {
      import: "import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'",
      usage: "<Sheet><SheetTrigger asChild><Button>Open</Button></SheetTrigger><SheetContent><SheetHeader><SheetTitle>Title</SheetTitle><SheetDescription>Description</SheetDescription></SheetHeader></SheetContent></Sheet>",
      subComponents: ["SheetContent", "SheetDescription", "SheetHeader", "SheetTitle", "SheetTrigger", "SheetFooter"]
    },
    "Popover": {
      import: "import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'",
      usage: "<Popover><PopoverTrigger asChild><Button>Open</Button></PopoverTrigger><PopoverContent>Content</PopoverContent></Popover>",
      subComponents: ["PopoverContent", "PopoverTrigger"]
    },
    "Tooltip": {
      import: "import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'",
      usage: "<TooltipProvider><Tooltip><TooltipTrigger asChild><Button>Hover me</Button></TooltipTrigger><TooltipContent>Tooltip content</TooltipContent></Tooltip></TooltipProvider>",
      subComponents: ["TooltipContent", "TooltipProvider", "TooltipTrigger"]
    },
    "Badge": {
      import: "import { Badge } from '@/components/ui/badge'",
      usage: "<Badge variant='default'>Badge</Badge>",
      variants: ["default", "secondary", "destructive", "outline"]
    },
    "Alert": {
      import: "import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'",
      usage: "<Alert><AlertTitle>Alert Title</AlertTitle><AlertDescription>Alert description</AlertDescription></Alert>",
      subComponents: ["AlertDescription", "AlertTitle"]
    },
    "Progress": {
      import: "import { Progress } from '@/components/ui/progress'",
      usage: "<Progress value={33} />"
    },
    "Skeleton": {
      import: "import { Skeleton } from '@/components/ui/skeleton'",
      usage: "<Skeleton className='w-[100px] h-[20px] rounded-full' />"
    },
    "Table": {
      import: "import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'",
      usage: "<Table><TableCaption>Caption</TableCaption><TableHeader><TableRow><TableHead>Header</TableHead></TableRow></TableHeader><TableBody><TableRow><TableCell>Cell</TableCell></TableRow></TableBody></Table>",
      subComponents: ["TableBody", "TableCaption", "TableCell", "TableHead", "TableHeader", "TableRow"]
    },
    "Avatar": {
      import: "import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'",
      usage: "<Avatar><AvatarImage src='/avatar.jpg' /><AvatarFallback>CN</AvatarFallback></Avatar>",
      subComponents: ["AvatarFallback", "AvatarImage"]
    },
    "Calendar": {
      import: "import { Calendar } from '@/components/ui/calendar'",
      usage: "<Calendar mode='single' selected={date} onSelect={setDate} />"
    },
    "Command": {
      import: "import { Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut } from '@/components/ui/command'",
      usage: "<Command><CommandInput placeholder='Type a command...' /><CommandList><CommandEmpty>No results found.</CommandEmpty><CommandGroup heading='Suggestions'><CommandItem>Item</CommandItem></CommandGroup></CommandList></Command>",
      subComponents: ["CommandDialog", "CommandEmpty", "CommandGroup", "CommandInput", "CommandItem", "CommandList", "CommandSeparator", "CommandShortcut"]
    }
  },
  PRICING_DESC: 'Start with a free account to speed up your workflow on public projects or boost your entire team with instantly-opening production environments.',
  PRICING_OPTIONS: [
    {
      name: 'Basic',
      tokens: '50K',
      value: 50000,
      desc: 'Ideal for hobbyists and casual users for light, exploratory use.',
      price: 4.99
    },
    {
      name: 'Starter',
      tokens: '120K',
      value: 120000,
      desc: 'Designed for professionals who need to use Bolt a few times per week.',
      price: 9.99
    },
    {
      name: 'Pro',
      tokens: '2.5M',
      value: 2500000,
      desc: 'Designed for professionals who need to use Bolt a few times per week.',
      price: 19.99
    },
    {
      name: 'Unlimted (License)',
      tokens: 'Unmited',
      value: *********,
      desc: 'Designed for professionals who need to use Bolt a few times per week.',
      price: 49.99
    }
  ]


}