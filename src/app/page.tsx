import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import AppLayout from "@/components/layout/app-layout"
import {
  ArrowRight,
  Zap,
  Code,
  Sparkles,
  Users,
  Clock,
  Star,
  Check,
  Github,
  Twitter,
  Linkedin,
  Palette,
  Rocket,
  Heart,
} from "lucide-react"

export default function LandingPage() {
  return (
    <AppLayout variant="landing" className="min-h-screen bg-black text-white overflow-hidden relative">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
        <div className="absolute top-1/2 right-1/4 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl animate-pulse delay-3000"></div>
      </div>

      {/* Floating Geometric Shapes */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rotate-45 animate-bounce delay-500"></div>
        <div className="absolute top-3/4 right-1/3 w-6 h-6 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full animate-bounce delay-1000"></div>
        <div className="absolute top-1/2 left-1/6 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-400 animate-bounce delay-1500"></div>
        <div className="absolute bottom-1/4 right-1/4 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-400 rotate-12 animate-bounce delay-2000"></div>
      </div>

      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <Badge
            variant="secondary"
            className="mb-8 bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-300 border border-purple-500/30 hover:bg-purple-500/30 transition-all duration-300"
          >
            <Sparkles className="w-4 h-4 mr-2 animate-spin" />
            AI-Powered App Development
          </Badge>

          <h1 className="text-5xl md:text-8xl font-black text-white mb-8 leading-tight">
            Build Apps You'll
            <br />
            <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-yellow-400 bg-clip-text text-transparent animate-pulse">
              Actually Love
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed font-light">
            Transform your wildest ideas into{" "}
            <span className="text-cyan-400 font-semibold">production-ready applications</span> in minutes, not months.
            Hateable's AI doesn't just understand your vision—it{" "}
            <span className="text-pink-400 font-semibold">brings it to life</span>.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Link href="/Thinkpad">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 hover:from-purple-600 hover:via-pink-600 hover:to-cyan-600 text-white text-xl px-12 py-4 rounded-2xl font-bold shadow-2xl hover:shadow-purple-500/50 transition-all duration-300 transform hover:scale-105"
              >
                <Rocket className="mr-3 w-6 h-6" />
                Start Building Free
                <ArrowRight className="ml-3 w-6 h-6" />
              </Button>
            </Link>
            <Link href="#features">
              <Button
                variant="outline"
                size="lg"
                className="text-xl px-12 py-4 rounded-2xl border-2 border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-500 transition-all duration-300 transform hover:scale-105 bg-transparent"
              >
                <Palette className="mr-3 w-6 h-6" />
                Watch Demo
              </Button>
            </Link>
          </div>

          {/* Hero Image with Creative Frame */}
          <div className="relative max-w-6xl mx-auto">
            <div className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black rounded-3xl shadow-2xl border border-gray-700 p-8 transform hover:scale-[1.02] transition-transform duration-500">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-cyan-500/10 rounded-3xl"></div>
              <img
                src="/placeholder.svg?height=500&width=900"
                alt="Hateable AI App Builder Interface"
                className="w-full h-auto rounded-2xl relative z-10"
              />
              <div className="absolute -top-6 -right-6 bg-gradient-to-r from-green-400 to-emerald-400 text-black px-6 py-3 rounded-2xl text-lg font-bold shadow-lg animate-bounce">
                ✨ Live Preview
              </div>
              <div className="absolute -bottom-4 -left-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-black px-4 py-2 rounded-xl text-sm font-semibold">
                🚀 Deploy Ready
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-6xl font-black text-white mb-6">
              Why Developers{" "}
              <span className="bg-gradient-to-r from-pink-400 to-cyan-400 bg-clip-text text-transparent">
                <Heart className="inline w-12 h-12 text-pink-400 mx-2" />
                Hateable
              </span>
            </h2>
            <p className="text-2xl text-gray-300 max-w-3xl mx-auto font-light">
              Experience the future of app development with AI that actually{" "}
              <span className="text-purple-400 font-semibold">gets you</span>
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: Code,
                title: "AI-First Development",
                description:
                  "Describe your app in plain English and watch as our AI generates clean, production-ready code that doesn't suck",
                gradient: "from-purple-500 to-pink-500",
                bgGradient: "from-purple-500/10 to-pink-500/10",
              },
              {
                icon: Clock,
                title: "Lightning Fast",
                description:
                  "Go from idea to deployed app in under 10 minutes. No more weeks of development cycles or endless debugging",
                gradient: "from-cyan-500 to-blue-500",
                bgGradient: "from-cyan-500/10 to-blue-500/10",
              },
              {
                icon: Users,
                title: "Team Collaboration",
                description:
                  "Built for teams that actually work together. Share, iterate, and deploy with real-time collaboration that just works",
                gradient: "from-yellow-500 to-orange-500",
                bgGradient: "from-yellow-500/10 to-orange-500/10",
              },
            ].map((feature, index) => (
              <Card
                key={index}
                className={`border-0 bg-gradient-to-br ${feature.bgGradient} backdrop-blur-xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 transform hover:scale-105 hover:-rotate-1 group`}
              >
                <CardHeader className="text-center">
                  <div
                    className={`w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 mx-auto transform group-hover:rotate-12 transition-transform duration-300`}
                  >
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-white mb-4">{feature.title}</CardTitle>
                  <CardDescription className="text-gray-300 text-lg leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-24 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-6xl font-black text-white mb-6">
              Loved by{" "}
              <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                10,000+
              </span>{" "}
              Developers
            </h2>
            <div className="flex items-center justify-center space-x-2 mb-6">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className="w-8 h-8 fill-yellow-400 text-yellow-400 animate-pulse"
                  style={{ animationDelay: `${i * 200}ms` }}
                />
              ))}
              <span className="ml-4 text-xl text-gray-300 font-semibold">4.9/5 from 2,847 reviews</span>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Chen",
                role: "Frontend Developer",
                content:
                  "Hateable turned my weekend project into a production app. The AI understood exactly what I wanted to build. Mind = blown! 🤯",
                avatar: "/placeholder.svg?height=60&width=60",
                gradient: "from-purple-500/20 to-pink-500/20",
              },
              {
                name: "Marcus Rodriguez",
                role: "Startup Founder",
                content:
                  "We shipped our MVP in 3 days instead of 3 months. Hateable is a game-changer for rapid prototyping. Our investors were shocked! 🚀",
                avatar: "/placeholder.svg?height=60&width=60",
                gradient: "from-cyan-500/20 to-blue-500/20",
              },
              {
                name: "Emily Watson",
                role: "Product Manager",
                content:
                  "Finally, an AI tool that doesn't fight me. It builds exactly what I describe, every single time. It's like having a mind reader! ✨",
                avatar: "/placeholder.svg?height=60&width=60",
                gradient: "from-yellow-500/20 to-orange-500/20",
              },
            ].map((testimonial, index) => (
              <Card
                key={index}
                className={`border border-gray-700 bg-gradient-to-br ${testimonial.gradient} backdrop-blur-xl shadow-2xl hover:shadow-cyan-500/25 transition-all duration-500 transform hover:scale-105 hover:rotate-1`}
              >
                <CardContent className="pt-8">
                  <div className="flex items-center space-x-1 mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-gray-200 mb-6 text-lg leading-relaxed font-medium">"{testimonial.content}"</p>
                  <div className="flex items-center space-x-4">
                    <img
                      src={testimonial.avatar || "/placeholder.svg"}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full border-2 border-gray-600"
                    />
                    <div>
                      <p className="font-bold text-white text-lg">{testimonial.name}</p>
                      <p className="text-gray-400">{testimonial.role}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-6xl font-black text-white mb-6">
              Simple,{" "}
              <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                Transparent
              </span>{" "}
              Pricing
            </h2>
            <p className="text-2xl text-gray-300 font-light">Start free, scale as you grow, love what you build</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                name: "Starter",
                description: "Perfect for side projects",
                price: "$0",
                period: "/month",
                features: ["3 apps per month", "Basic AI assistance", "Community support", "Standard templates"],
                buttonText: "Get Started Free",
                buttonStyle: "border-gray-600 text-gray-300 hover:bg-gray-800",
                popular: false,
              },
              {
                name: "Pro",
                description: "For serious developers",
                price: "$29",
                period: "/month",
                features: [
                  "Unlimited apps",
                  "Advanced AI features",
                  "Priority support",
                  "Team collaboration",
                  "Custom templates",
                  "API access",
                ],
                buttonText: "Start Pro Trial",
                buttonStyle:
                  "bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 hover:from-purple-600 hover:via-pink-600 hover:to-cyan-600",
                popular: true,
              },
              {
                name: "Enterprise",
                description: "For large teams",
                price: "Custom",
                period: "",
                features: [
                  "Everything in Pro",
                  "Custom integrations",
                  "Dedicated support",
                  "SLA guarantee",
                  "White-label options",
                  "Custom AI training",
                ],
                buttonText: "Contact Sales",
                buttonStyle: "border-gray-600 text-gray-300 hover:bg-gray-800",
                popular: false,
              },
            ].map((plan, index) => (
              <Card
                key={index}
                className={`relative border ${plan.popular ? "border-purple-500 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-cyan-500/10" : "border-gray-700 bg-gradient-to-br from-gray-900/50 to-black/50"} backdrop-blur-xl shadow-2xl transition-all duration-500 transform hover:scale-105 ${plan.popular ? "hover:shadow-purple-500/50" : "hover:shadow-cyan-500/25"}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 text-sm font-bold animate-pulse">
                      🔥 Most Popular
                    </Badge>
                  </div>
                )}
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl font-bold text-white mb-2">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-300 text-lg mb-6">{plan.description}</CardDescription>
                  <div className="mb-6">
                    <span className="text-5xl font-black text-white">{plan.price}</span>
                    <span className="text-gray-400 text-xl">{plan.period}</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-200">
                        <Check className="w-5 h-5 text-green-400 mr-4 flex-shrink-0" />
                        <span className="text-lg">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link href={plan.name === "Starter" ? "/Thinkpad" : plan.name === "Enterprise" ? "#contact" : "/Thinkpad"}>
                    <Button
                      className={`w-full text-lg py-3 font-bold transition-all duration-300 transform hover:scale-105 ${plan.buttonStyle}`}
                      variant={plan.popular ? "default" : "outline"}
                    >
                      {plan.buttonText}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-4 sm:px-6 lg:px-8 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-cyan-600/20 backdrop-blur-3xl"></div>
        <div className="max-w-5xl mx-auto text-center relative z-10">
          <h2 className="text-4xl md:text-6xl font-black text-white mb-8">
            Ready to Build Something{" "}
            <span className="bg-gradient-to-r from-yellow-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
              Amazing?
            </span>
          </h2>
          <p className="text-2xl text-gray-300 mb-12 font-light">
            Join thousands of developers who are shipping faster, building better, and loving every minute of it
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link href="/Thinkpad">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 hover:from-purple-600 hover:via-pink-600 hover:to-cyan-600 text-white text-xl px-12 py-4 font-bold shadow-2xl hover:shadow-purple-500/50 transition-all duration-300 transform hover:scale-110"
              >
                <Rocket className="mr-3 w-6 h-6" />
                Start Building Now
                <ArrowRight className="ml-3 w-6 h-6" />
              </Button>
            </Link>
            <Link href="#contact">
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-500 text-gray-300 hover:bg-gray-800 hover:text-white text-xl px-12 py-4 font-bold transition-all duration-300 transform hover:scale-110 bg-transparent"
              >
                <Palette className="mr-3 w-6 h-6" />
                Schedule Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-24 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h2 className="text-4xl md:text-5xl font-black text-white mb-8">
            Get in{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Touch
            </span>
          </h2>
          <p className="text-xl text-gray-300 mb-12 font-light">
            Have questions? Want to see a demo? We'd love to hear from you.
          </p>

          <div className="grid md:grid-cols-2 gap-8 max-w-2xl mx-auto">
            <Card className="border border-gray-700 bg-gradient-to-br from-gray-900/50 to-black/50 backdrop-blur-xl">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold text-white mb-4">Schedule a Demo</h3>
                <p className="text-gray-300 mb-6">See Hateable in action with a personalized demo</p>
                <Link href="mailto:<EMAIL>">
                  <Button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                    Book Demo Call
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="border border-gray-700 bg-gradient-to-br from-gray-900/50 to-black/50 backdrop-blur-xl">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold text-white mb-4">Enterprise Sales</h3>
                <p className="text-gray-300 mb-6">Custom solutions for your organization</p>
                <Link href="mailto:<EMAIL>">
                  <Button variant="outline" className="w-full border-gray-600 text-gray-300 hover:bg-gray-800">
                    Contact Sales
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white py-16 px-4 sm:px-6 lg:px-8 border-t border-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 via-pink-500 to-cyan-500 rounded-2xl flex items-center justify-center transform rotate-12">
                  <Zap className="w-7 h-7 text-white" />
                </div>
                <span className="text-2xl font-black bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                  Hateable
                </span>
              </div>
              <p className="text-gray-400 mb-6 text-lg">
                The AI app builder that actually works. Build what you love, love what you build.
              </p>
              <div className="flex space-x-6">
                <Link href="https://twitter.com/hateable_dev" target="_blank" rel="noopener noreferrer">
                  <Twitter className="w-6 h-6 text-gray-400 hover:text-cyan-400 cursor-pointer transition-colors duration-300" />
                </Link>
                <Link href="https://github.com/hateable-dev" target="_blank" rel="noopener noreferrer">
                  <Github className="w-6 h-6 text-gray-400 hover:text-white cursor-pointer transition-colors duration-300" />
                </Link>
                <Link href="https://linkedin.com/company/hateable" target="_blank" rel="noopener noreferrer">
                  <Linkedin className="w-6 h-6 text-gray-400 hover:text-blue-400 cursor-pointer transition-colors duration-300" />
                </Link>
              </div>
            </div>

            {[
              {
                title: "Product",
                links: [
                  { name: "Features", href: "#features" },
                  { name: "Pricing", href: "#pricing" },
                  { name: "Documentation", href: "/docs" },
                  { name: "API", href: "/api" },
                  { name: "Templates", href: "/templates" },
                ],
              },
              {
                title: "Company",
                links: [
                  { name: "About", href: "/about" },
                  { name: "Blog", href: "/blog" },
                  { name: "Careers", href: "/careers" },
                  { name: "Contact", href: "#contact" },
                  { name: "Press", href: "/press" },
                ],
              },
              {
                title: "Support",
                links: [
                  { name: "Help Center", href: "/help" },
                  { name: "Community", href: "/community" },
                  { name: "Status", href: "/status" },
                  { name: "Privacy", href: "/privacy" },
                  { name: "Terms", href: "/terms" },
                ],
              },
            ].map((section, index) => (
              <div key={index}>
                <h3 className="font-bold mb-6 text-xl text-white">{section.title}</h3>
                <ul className="space-y-3">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <Link href={link.href} className="text-gray-400 hover:text-white transition-colors duration-300 text-lg">
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 text-center">
            <p className="text-gray-400 text-lg">
              &copy; 2024 Hateable. All rights reserved. Made with{" "}
              <Heart className="inline w-5 h-5 text-pink-400 mx-1" />
              by developers, for developers.
            </p>
          </div>
        </div>
      </footer>
    </AppLayout>
  )
}
