import { GenAICode } from "@/config/AiConfig"
import { NextResponse } from "next/server"
import { ShadcnComponentDetector } from "@/lib/shadcn-detector"
import Lookup from "@/data/Lookup"

export async function POST(req: Request) {
    try {
        const { prompt, existingFiles, editType = "feature" } = await req.json()

        // Create a context-aware prompt for incremental editing
        const contextPrompt = createIncrementalEditPrompt(prompt, existingFiles, editType);
        
        const response = await GenAICode.sendMessage(contextPrompt)
        const result = response.response.text()
        
        if (!result || result.trim() === "") {
            throw new Error("Empty response from AI");
        }

        const parsed = JSON.parse(result);

        // Enhance the response with shadcn component integration
        const enhancedResponse = await enhanceIncrementalEdit(parsed, existingFiles);

        return NextResponse.json(enhancedResponse)
    } catch (error: any) {
        console.error("Error processing incremental edit request:", error)
        return NextResponse.json({
            error: error.message || "Internal Server Error"
        })
    }
}

function createIncrementalEditPrompt(userPrompt: string, existingFiles: any, editType: string): string {
    const filesList = Object.keys(existingFiles || {}).join(', ');
    const existingCode = Object.entries(existingFiles || {})
        .map(([path, file]: [string, any]) => `// ${path}\n${file.code || ''}`)
        .join('\n\n');

    return `
INCREMENTAL CODE EDITING REQUEST

User Request: ${userPrompt}
Edit Type: ${editType}

EXISTING PROJECT FILES:
${filesList}

EXISTING CODE CONTEXT:
${existingCode}

INSTRUCTIONS:
You are performing an INCREMENTAL EDIT, not a complete rewrite. Follow these rules:

1. ANALYZE the existing code structure and understand the current implementation
2. IDENTIFY which files need to be modified and which new files need to be created
3. MAKE MINIMAL CHANGES - only modify what's necessary for the requested feature
4. PRESERVE existing functionality and code structure
5. USE shadcn/ui components when adding new UI elements
6. MAINTAIN consistent coding patterns from the existing codebase

RESPONSE FORMAT:
Return a JSON response with this exact structure:
{
  "editType": "incremental",
  "summary": "Brief description of changes made",
  "newFiles": {
    "/path/to/new/file.js": {
      "code": "complete new file content",
      "reason": "why this file was created"
    }
  },
  "modifiedFiles": {
    "/path/to/existing/file.js": {
      "code": "complete updated file content",
      "changes": "description of what was changed",
      "reason": "why this file was modified"
    }
  },
  "unchangedFiles": ["/path/to/file1.js", "/path/to/file2.js"],
  "usageInstructions": "How to use the new feature",
  "usedShadcnComponents": ["Button", "Input", "Card"]
}

IMPORTANT RULES:
- Only include files that actually need changes in "modifiedFiles"
- List files that don't need changes in "unchangedFiles"
- Provide complete file content for both new and modified files
- Explain your changes clearly
- Use relative imports (not @/ paths)
- Maintain existing file structure and naming conventions
- Use shadcn/ui components for new UI elements

Generate the incremental edit now:
`;
}

async function enhanceIncrementalEdit(aiResponse: any, existingFiles: any) {
    try {
        // Combine all new and modified code to analyze for shadcn components
        const allNewCode = [
            ...Object.values(aiResponse.newFiles || {}),
            ...Object.values(aiResponse.modifiedFiles || {})
        ].map((file: any) => file.code || '').join('\n');

        // Detect shadcn components in the new/modified code
        const detection = ShadcnComponentDetector.detectComponents(allNewCode);

        // Add utility files if new components are detected
        if (detection.detectedComponents.length > 0) {
            const utilsFiles = ShadcnComponentDetector.generateUtilsFile();
            const componentFiles = ShadcnComponentDetector.generateComponentFiles(detection.detectedComponents);
            
            // Only add utils and component files if they don't already exist
            Object.entries(utilsFiles).forEach(([path, file]) => {
                if (!existingFiles[path]) {
                    aiResponse.newFiles = aiResponse.newFiles || {};
                    aiResponse.newFiles[path] = {
                        ...file,
                        reason: "Required utility file for shadcn components"
                    };
                }
            });

            Object.entries(componentFiles).forEach(([path, file]) => {
                if (!existingFiles[path]) {
                    aiResponse.newFiles = aiResponse.newFiles || {};
                    aiResponse.newFiles[path] = {
                        ...file,
                        reason: `shadcn/ui component: ${path.split('/').pop()?.replace('.tsx', '')}`
                    };
                }
            });
        }

        // Add metadata about detected components
        aiResponse.detectedShadcnComponents = detection.detectedComponents;
        aiResponse.requiredShadcnImports = detection.requiredImports;

        return aiResponse;
    } catch (error) {
        console.error("Error enhancing incremental edit:", error);
        return aiResponse;
    }
}
