import { GenAICode } from "@/config/AiConfig"
import { NextResponse } from "next/server"
import { ShadcnComponentDetector } from "@/lib/shadcn-detector"
import Lookup from "@/data/Lookup"

export async function POST(req:Request) {
    try {
        const {prompt} = await req.json()

        const response = await GenAICode.sendMessage(prompt)
        const result = response.response.text()
        if (!result || result.trim() === "") {
            throw new Error("Empty response from AI");
        }

        const parsed = JSON.parse(result);

        // Enhance the response with shadcn component integration
        const enhancedResponse = await enhanceWithShadcnComponents(parsed);

        return NextResponse.json(enhancedResponse)
    } catch (error: any) {
        console.error("Error processing request:", error)
        return NextResponse.json({
            error: error.message || "Internal Server Error"
        })
    }
}

async function enhanceWithShadcnComponents(aiResponse: any) {
    try {
        // Combine all code from files to analyze
        const allCode = Object.values(aiResponse.files || {})
            .map((file: any) => file.code || '')
            .join('\n');

        // Detect shadcn components that should be used
        const detection = ShadcnComponentDetector.detectComponents(allCode);

        // Add utility files if components are detected
        if (detection.detectedComponents.length > 0) {
            const utilsFiles = ShadcnComponentDetector.generateUtilsFile();
            aiResponse.files = { ...aiResponse.files, ...utilsFiles };
        }

        // Add component reference files
        const componentFiles = ShadcnComponentDetector.generateComponentFiles(detection.detectedComponents);
        aiResponse.files = { ...aiResponse.files, ...componentFiles };

        // Update dependencies with required shadcn dependencies
        const updatedDependencies = {
            ...Lookup.DEPENDENCY,
            ...detection.requiredDependencies
        };

        // Add metadata about detected components
        aiResponse.detectedShadcnComponents = detection.detectedComponents;
        aiResponse.requiredShadcnImports = detection.requiredImports;
        aiResponse.dependencies = updatedDependencies;

        // Update the explanation to mention shadcn usage
        if (detection.detectedComponents.length > 0) {
            const componentNames = detection.detectedComponents.map(c => c.name).join(', ');
            aiResponse.explanation = `${aiResponse.explanation} This project uses shadcn/ui components (${componentNames}) for a modern, accessible UI with proper TypeScript support and consistent styling.`;
        }

        return aiResponse;
    } catch (error) {
        console.error("Error enhancing with shadcn components:", error);
        // Return original response if enhancement fails
        return aiResponse;
    }
}