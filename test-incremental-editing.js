// Test script to demonstrate incremental editing functionality
// This would be run in a browser console or as a Node.js script

const testIncrementalEditing = () => {
  console.log('🧪 Testing Incremental Editing Feature\n');

  // Mock existing files (simulating a simple todo app)
  const existingFiles = {
    '/App.js': {
      code: `import React, { useState } from 'react';
import TodoItem from './components/TodoItem';

function App() {
  const [todos, setTodos] = useState([
    { id: 1, text: 'Learn React', completed: false },
    { id: 2, text: 'Build todo app', completed: true }
  ]);

  const addTodo = (text) => {
    const newTodo = {
      id: Date.now(),
      text,
      completed: false
    };
    setTodos([...todos, newTodo]);
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Todo App</h1>
      <div className="space-y-2">
        {todos.map(todo => (
          <TodoItem key={todo.id} todo={todo} />
        ))}
      </div>
    </div>
  );
}

export default App;`
    },
    '/components/TodoItem.js': {
      code: `import React from 'react';

function TodoItem({ todo }) {
  return (
    <div className="flex items-center p-3 border rounded">
      <input 
        type="checkbox" 
        checked={todo.completed}
        className="mr-3"
      />
      <span className={todo.completed ? 'line-through text-gray-500' : ''}>
        {todo.text}
      </span>
    </div>
  );
}

export default TodoItem;`
    }
  };

  // Test cases for different types of incremental edits
  const testCases = [
    {
      name: 'Add Delete Feature',
      prompt: 'Add a delete button to each todo item',
      expectedEditType: 'feature',
      expectedChanges: {
        newFiles: 0,
        modifiedFiles: 2, // App.js and TodoItem.js
        shouldPreserve: ['/components/TodoItem.js structure']
      }
    },
    {
      name: 'Create Search Component',
      prompt: 'Create a search bar to filter todos',
      expectedEditType: 'feature',
      expectedChanges: {
        newFiles: 1, // SearchBar component
        modifiedFiles: 1, // App.js
        shouldPreserve: ['/components/TodoItem.js']
      }
    },
    {
      name: 'Fix Bug',
      prompt: 'Fix the checkbox not updating todo completion status',
      expectedEditType: 'bugfix',
      expectedChanges: {
        newFiles: 0,
        modifiedFiles: 2, // App.js and TodoItem.js
        shouldPreserve: ['existing todo structure']
      }
    },
    {
      name: 'Style Update',
      prompt: 'Make the todo items look more modern with better styling',
      expectedEditType: 'style',
      expectedChanges: {
        newFiles: 0,
        modifiedFiles: 1, // TodoItem.js
        shouldPreserve: ['/App.js logic']
      }
    },
    {
      name: 'Full Rewrite Trigger',
      prompt: 'Rebuild the entire app from scratch with a different approach',
      expectedEditType: 'full',
      expectedChanges: {
        shouldTriggerFullRegeneration: true
      }
    }
  ];

  // Test the IncrementalEditor logic
  testCases.forEach((testCase, index) => {
    console.log(`\n${index + 1}. Testing: ${testCase.name}`);
    console.log(`   Prompt: "${testCase.prompt}"`);
    
    // This would normally call the IncrementalEditor.shouldUseIncrementalEdit method
    const shouldUseIncremental = !testCase.prompt.toLowerCase().includes('rebuild') && 
                                !testCase.prompt.toLowerCase().includes('from scratch');
    
    const editType = determineEditType(testCase.prompt);
    
    console.log(`   ✅ Edit Mode: ${shouldUseIncremental ? 'Incremental' : 'Full Regeneration'}`);
    console.log(`   ✅ Edit Type: ${editType}`);
    
    if (shouldUseIncremental) {
      console.log(`   📝 Expected Changes:`);
      if (testCase.expectedChanges.newFiles > 0) {
        console.log(`      - ${testCase.expectedChanges.newFiles} new file(s)`);
      }
      if (testCase.expectedChanges.modifiedFiles > 0) {
        console.log(`      - ${testCase.expectedChanges.modifiedFiles} modified file(s)`);
      }
      if (testCase.expectedChanges.shouldPreserve) {
        console.log(`      - Preserve: ${testCase.expectedChanges.shouldPreserve.join(', ')}`);
      }
    }
  });

  console.log('\n🎉 Incremental Editing Tests Complete!');
  console.log('\n📋 Summary of Benefits:');
  console.log('   ✅ Preserves existing code structure');
  console.log('   ✅ Makes targeted changes only');
  console.log('   ✅ Provides clear change summaries');
  console.log('   ✅ Faster than full regeneration');
  console.log('   ✅ Reduces risk of breaking existing features');
};

// Helper function to determine edit type (simplified version)
function determineEditType(prompt) {
  const promptLower = prompt.toLowerCase();
  
  if (promptLower.includes('bug') || promptLower.includes('fix')) {
    return 'bugfix';
  }
  if (promptLower.includes('style') || promptLower.includes('styling') || promptLower.includes('modern')) {
    return 'style';
  }
  if (promptLower.includes('component') || promptLower.includes('create')) {
    return 'component';
  }
  return 'feature';
}

// Example API call structure for incremental editing
const exampleApiCall = {
  endpoint: '/api/edit-code',
  method: 'POST',
  body: {
    prompt: 'Add a delete button to each todo item',
    existingFiles: {
      '/App.js': { code: '...' },
      '/components/TodoItem.js': { code: '...' }
    },
    editType: 'feature'
  },
  expectedResponse: {
    editType: 'incremental',
    summary: 'Added delete functionality to todo items',
    newFiles: {},
    modifiedFiles: {
      '/App.js': {
        code: '// Updated App.js with delete function',
        changes: 'Added deleteTodo function and passed to TodoItem',
        reason: 'Enable todo deletion functionality'
      },
      '/components/TodoItem.js': {
        code: '// Updated TodoItem.js with delete button',
        changes: 'Added delete button and onClick handler',
        reason: 'Add delete button to each todo item'
      }
    },
    unchangedFiles: [],
    usageInstructions: 'Click the delete button on any todo item to remove it',
    usedShadcnComponents: ['Button']
  }
};

// Run the test
if (typeof window !== 'undefined') {
  // Browser environment
  console.log('Run testIncrementalEditing() in the console to see the demo');
} else {
  // Node.js environment
  testIncrementalEditing();
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testIncrementalEditing, exampleApiCall };
}
